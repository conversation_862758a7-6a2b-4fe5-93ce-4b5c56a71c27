import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/theme/app_colors.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/chat/floating_chat_input.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 音频房间聊天输入组件
/// 用于在控制栏中显示聊天输入框
class AudioRoomChatInput extends ConsumerWidget {
  const AudioRoomChatInput({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final audioRoomState = ref.watch(audioRoomProvider);
    final isEnabled = audioRoomState.currentRoom != null;

    return GestureDetector(
      onTap: isEnabled ? () => _showFloatingInput(context, ref) : null,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 12.w,
          vertical: 8.h,
        ),
        decoration: BoxDecoration(
          color: const Color(0xFF262626),
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Text(
          'Comment...',
          style: TextStyle(
            color: Colors.grey.withValues(alpha: 0.7),
            fontSize: 14.sp,
          ),
        ),
      ),
    );
  }

  /// 显示悬浮输入框
  void _showFloatingInput(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: FloatingChatInput(
            onSendMessage: (message) {
              _handleSendMessage(context, ref, message);
            },
            enabled: true,
            hintText: 'Comment...',
            maxLength: 200,
            onClose: () => Navigator.pop(context),
          ),
        );
      },
    );
  }

  /// 处理发送消息
  Future<void> _handleSendMessage(
      BuildContext context, WidgetRef ref, String message) async {
    final audioRoomState = ref.read(audioRoomProvider);
    final isChatInitialized =
        ref.read(audioRoomProvider.notifier).isChatServiceInitialized;

    if (!isChatInitialized) {
      _showError(context, 'Chat not initialized');
      return;
    }

    final currentUser = audioRoomState.currentUser;
    final currentUid = audioRoomState.currentUid;
    final roomId = audioRoomState.currentRoom?.id.toString();

    if (currentUser == null || currentUid == null) {
      _showError(context, 'Failed to get user information');
      return;
    }

    LogUtils.d('Sending chat message: $message', tag: 'AudioRoomChatInput');

    // 通过AudioRoomProvider发送聊天消息
    final result = await ref.read(audioRoomProvider.notifier).sendChatMessage(
          content: message,
          roomId: roomId,
        );

    if (result.isLeft()) {
      LogUtils.e(
          'Failed to send message: ${result.fold((l) => l.toString(), (r) => 'Unknown')}',
          tag: 'AudioRoomChatInput');
      if (context.mounted) {
        _showError(context, 'Failed to send message');
      }
    } else {
      LogUtils.d('Message sent successfully', tag: 'AudioRoomChatInput');
      // 发送成功，消息会通过流自动添加到UI
    }
  }

  /// 显示错误消息
  void _showError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
