import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/app_screen_utils.dart';
import 'package:flutter_audio_room/core/widgets/avatar_with_frame.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

/// 聊天消息项组件
class ChatMessageItem extends StatelessWidget {
  final ChatMessageModel message;
  final bool showAvatar;
  final bool showTimestamp;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const ChatMessageItem({
    super.key,
    required this.message,
    this.showAvatar = true,
    this.showTimestamp = false,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showTimestamp) _buildTimestamp(context),
          _buildMessageContent(context),
        ],
      ),
    );
  }

  /// 构建时间戳
  Widget _buildTimestamp(BuildContext context) {
    final time = DateTime.fromMillisecondsSinceEpoch(message.timestamp);
    final timeString = DateFormat('HH:mm').format(time);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 4.0),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: Text(
            timeString,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建消息内容
  Widget _buildMessageContent(BuildContext context) {
    return _buildUnifiedMessage(context);
  }

  /// 构建统一的消息布局
  Widget _buildUnifiedMessage(BuildContext context) {
    final messageConfig = _getMessageConfig(context);
    final Color messageBackgroundColor =
        context.theme.colorScheme.surface.withValues(alpha: 0.3);

    return Container(
      decoration: BoxDecoration(
        color: messageBackgroundColor,
        borderRadius: BorderRadius.circular(
          AppScreenUtils.setRadius(14),
        ),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: AppScreenUtils.setWidth(5),
        vertical: AppScreenUtils.setWidth(5),
      ).copyWith(right: AppScreenUtils.setWidth(8)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 左侧图标或头像
          _buildLeadingWidget(messageConfig),
          SizedBox(width: 8.w),
          // 消息内容
          Flexible(
            child: messageConfig.showSenderName
                ? Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: '${messageConfig.senderPrefix}: ',
                          style: context.theme.textTheme.bodySmall?.copyWith(
                            color: messageConfig.senderColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        TextSpan(
                          text: message.content,
                          style: context.theme.textTheme.bodySmall,
                        ),
                      ],
                    ),
                    style: context.theme.textTheme.bodySmall,
                  )
                : Text(
                    message.content,
                    style: context.theme.textTheme.bodySmall,
                  ),
          ),
        ],
      ),
    );
  }

  /// 构建左侧头像
  Widget _buildLeadingWidget(MessageConfig config) {
    // 根据消息类型选择头像
    String avatarUrl;
    if (message.type == ChatMessageType.text) {
      // 文字消息显示发送者头像
      avatarUrl = message.senderAvatar ?? '';
    } else {
      // 非文字消息显示target user头像（如果有的话，否则显示发送者头像）
      avatarUrl = config.targetUserAvatar ?? message.senderAvatar ?? '';
    }

    return AvatarWithFrame(
      avatarUrl: avatarUrl,
      width: 18.w,
      height: 18.w,
    );
  }

  /// 获取消息配置
  MessageConfig _getMessageConfig(BuildContext context) {
    switch (message.type) {
      case ChatMessageType.text:
        return MessageConfig(
          showSenderName: true,
          senderPrefix: message.senderName,
          senderColor: context.theme.colorScheme.primary,
        );
      case ChatMessageType.system:
      case ChatMessageType.notification:
        return MessageConfig(
          showSenderName: false,
          senderPrefix: '',
          senderColor: context.theme.colorScheme.primary,
          targetUserAvatar: _getTargetUserAvatar(),
        );
    }
  }

  /// 获取target user头像
  String? _getTargetUserAvatar() {
    // 从extra中获取target user信息
    final extra = message.extra;
    if (extra != null && extra.containsKey('targetUserAvatar')) {
      return extra['targetUserAvatar'] as String?;
    }
    return null;
  }
}

/// 消息配置类
class MessageConfig {
  final bool showSenderName;
  final String senderPrefix;
  final Color senderColor;
  final String? targetUserAvatar;

  const MessageConfig({
    required this.showSenderName,
    required this.senderPrefix,
    required this.senderColor,
    this.targetUserAvatar,
  });
}
