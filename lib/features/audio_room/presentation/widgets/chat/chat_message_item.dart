import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/app_screen_utils.dart';
import 'package:flutter_audio_room/core/widgets/avatar_with_frame.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

/// 聊天消息项组件
class ChatMessageItem extends StatelessWidget {
  final ChatMessageModel message;
  final bool showAvatar;
  final bool showTimestamp;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const ChatMessageItem({
    super.key,
    required this.message,
    this.showAvatar = true,
    this.showTimestamp = false,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showTimestamp) _buildTimestamp(context),
          _buildMessageContent(context),
        ],
      ),
    );
  }

  /// 构建时间戳
  Widget _buildTimestamp(BuildContext context) {
    final time = DateTime.fromMillisecondsSinceEpoch(message.timestamp);
    final timeString = DateFormat('HH:mm').format(time);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 4.0),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: Text(
            timeString,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建消息内容
  Widget _buildMessageContent(BuildContext context) {
    switch (message.type) {
      case ChatMessageType.text:
        return _buildTextMessage(context);
      case ChatMessageType.system:
        return _buildSystemMessage(context);
      case ChatMessageType.notification:
        return _buildNotificationMessage(context);
    }
  }

  /// 构建文字消息
  Widget _buildTextMessage(BuildContext context) {
    final Color messageBackgroundColor =
        context.theme.colorScheme.surface.withValues(alpha: 0.3);

    return Container(
      decoration: BoxDecoration(
        color: messageBackgroundColor,
        borderRadius: BorderRadius.circular(
          AppScreenUtils.setRadius(14),
        ),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: AppScreenUtils.setWidth(5),
        vertical: AppScreenUtils.setWidth(5),
      ).copyWith(right: AppScreenUtils.setWidth(8)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (showAvatar) ...[
            AvatarWithFrame(
              avatarUrl: message.senderAvatar ?? '',
              width: 18.w,
              height: 18.w,
            ),
            SizedBox(width: 8.w),
          ],
          Flexible(
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: '${message.senderName}: ',
                    style: context.theme.textTheme.bodySmall?.copyWith(
                      color: context.theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextSpan(
                    text: message.content,
                    style: context.theme.textTheme.bodySmall,
                  ),
                ],
              ),
              style: context.theme.textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建系统消息
  Widget _buildSystemMessage(BuildContext context) {
    final Color messageBackgroundColor =
        context.theme.colorScheme.surface.withValues(alpha: 0.3);

    return Container(
      decoration: BoxDecoration(
        color: messageBackgroundColor,
        borderRadius: BorderRadius.circular(
          AppScreenUtils.setRadius(14), // 与文字消息保持一致
        ),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: AppScreenUtils.setWidth(5),
        vertical: AppScreenUtils.setWidth(5),
      ).copyWith(right: AppScreenUtils.setWidth(8)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 系统消息图标
          Icon(
            Icons.info_outline,
            size: 18.w,
            color: context.theme.colorScheme.primary,
          ),
          SizedBox(width: 8.w),
          Flexible(
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: 'System: ',
                    style: context.theme.textTheme.bodySmall?.copyWith(
                      color: context.theme.colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextSpan(
                    text: message.content,
                    style: context.theme.textTheme.bodySmall,
                  ),
                ],
              ),
              style: context.theme.textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建通知消息
  Widget _buildNotificationMessage(BuildContext context) {
    final Color messageBackgroundColor =
        context.theme.colorScheme.surface.withValues(alpha: 0.3);

    return Container(
      decoration: BoxDecoration(
        color: messageBackgroundColor,
        borderRadius: BorderRadius.circular(
          AppScreenUtils.setRadius(14), // 与文字消息保持一致
        ),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: AppScreenUtils.setWidth(5),
        vertical: AppScreenUtils.setWidth(5),
      ).copyWith(right: AppScreenUtils.setWidth(8)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 通知消息图标
          Icon(
            Icons.notifications_outlined,
            size: 18.w,
            color: context.theme.colorScheme.secondary,
          ),
          SizedBox(width: 8.w),
          Flexible(
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: 'Notice: ',
                    style: context.theme.textTheme.bodySmall?.copyWith(
                      color: context.theme.colorScheme.secondary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextSpan(
                    text: message.content,
                    style: context.theme.textTheme.bodySmall,
                  ),
                ],
              ),
              style: context.theme.textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }
}
