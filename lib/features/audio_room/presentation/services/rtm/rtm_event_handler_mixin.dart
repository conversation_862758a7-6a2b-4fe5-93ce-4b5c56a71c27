import 'package:agora_rtm/agora_rtm.dart' as agora;
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_rtm_service.dart';

mixin RtmEventHandlerMixin on IRtmService {
  void Function(agora.StorageEvent event)? onStorageEvent;
  void Function(agora.PresenceEvent event)? onPresenceEvent;
  void Function(agora.TopicEvent event)? onTopicEvent;
  void Function(agora.TokenEvent event)? onTokenPrivilegeWillExpire;
  void Function(agora.LinkStateEvent event)? onLinkStateEvent;

  // 支持多个消息监听器
  final List<void Function(agora.MessageEvent event)> _messageEventListeners =
      [];

  // 兼容性属性 - 保持向后兼容
  void Function(agora.MessageEvent event)? get onMessageEvent =>
      _messageEventListeners.isNotEmpty ? _messageEventListeners.first : null;

  @override
  set onMessageEvent(void Function(agora.MessageEvent event)? callback) {
    _messageEventListeners.clear();
    if (callback != null) {
      _messageEventListeners.add(callback);
    }
  }

  // 新的方法：添加消息监听器
  @override
  void addMessageEventListener(
      void Function(agora.MessageEvent event) listener) {
    _messageEventListeners.add(listener);
  }

  // 新的方法：移除消息监听器
  @override
  void removeMessageEventListener(
      void Function(agora.MessageEvent event) listener) {
    _messageEventListeners.remove(listener);
  }

  // 新的方法：清除所有消息监听器
  @override
  void clearMessageEventListeners() {
    _messageEventListeners.clear();
  }
  
  @override
  void setupEventListeners(agora.RtmClient? client) {
    client?.addListener(
      storage: _handleStorageEvent,
      presence: _handlePresenceEvent,
      message: _handleMessageEvent,
      topic: _handleTopicEvent,
      token: _handleTokenEvent,
      linkState: _handleLinkStateEvent,
    );
  }
  @override
  void removeEventListeners(agora.RtmClient? client) {
    client?.removeListener(
      storage: _handleStorageEvent,
      presence: _handlePresenceEvent,
      message: _handleMessageEvent,
      topic: _handleTopicEvent,
      token: _handleTokenEvent,
    );
  }

  void _handleStorageEvent(agora.StorageEvent event) {
    onStorageEvent?.call(event);
  }

  void _handlePresenceEvent(agora.PresenceEvent event) {
    onPresenceEvent?.call(event);
  }

  void _handleMessageEvent(agora.MessageEvent event) {
    if (event.message != null) {
      // 调用所有消息监听器
      for (final listener in _messageEventListeners) {
        try {
          listener(event);
        } catch (e) {
          LogUtils.e('Error in message event listener: $e',
              tag: 'RtmEventHandlerMixin');
        }
      }
    }
  }

  void _handleTopicEvent(agora.TopicEvent event) {
    LogUtils.d('Topic event: ${event.toJson()}', tag: 'RtmService.topic');
    onTopicEvent?.call(event);
  }

  void _handleTokenEvent(agora.TokenEvent event) {
    LogUtils.d(
      'Token will expire for channel: ${event.channelName}',
      tag: 'RtmService.token',
    );
    onTokenPrivilegeWillExpire?.call(event);
  }

  void _handleLinkStateEvent(agora.LinkStateEvent event) {
    LogUtils.d('Link state event: ${event.toJson()}',
        tag: 'RtmService.linkState');
    onLinkStateEvent?.call(event);
  }
}
