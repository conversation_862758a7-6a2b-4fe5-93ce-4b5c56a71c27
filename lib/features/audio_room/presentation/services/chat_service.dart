import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:agora_rtm/agora_rtm.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_enums.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_rtm_service.dart';
import 'package:flutter_audio_room/features/audio_room/domain/services/chat_message_stream.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/handlers/message/chat_message_handler.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';

/// 聊天服务
/// 整合RTM Message Channel和聊天消息处理的完整服务
class ChatService {
  final IRtmService _rtmService;
  final ChatMessageStream _messageStream;
  final ChatMessageHandler _messageHandler;

  StreamSubscription? _topicEventSubscription;
  StreamSubscription? _messageEventSubscription;

  // 保存消息监听器的引用，用于后续移除
  late final void Function(MessageEvent) _chatMessageListener;

  ChatService(this._rtmService)
      : _messageStream = ChatMessageStream(),
        _messageHandler = ChatMessageHandler(ChatMessageStream()) {
    // 注意：这里创建了两个不同的 ChatMessageStream 实例
    // 需要确保使用同一个实例，但由于 _messageHandler 是 final，
    // 我们需要在构造函数中正确初始化
  }

  // 工厂构造函数确保使用同一个消息流
  factory ChatService.create(IRtmService rtmService) {
    final messageStream = ChatMessageStream();
    final messageHandler = ChatMessageHandler(messageStream);
    return ChatService._internal(rtmService, messageStream, messageHandler);
  }

  ChatService._internal(
      this._rtmService, this._messageStream, this._messageHandler);

  /// 获取聊天消息流
  Stream<ChatMessageModel> get messageStream => _messageStream.stream;

  /// 获取文字消息流
  Stream<ChatMessageModel> get textMessageStream => _messageStream.textMessages;

  /// 获取系统消息流
  Stream<ChatMessageModel> get systemMessageStream =>
      _messageStream.systemMessages;

  /// 初始化聊天服务
  Future<VoidResult> initialize() async {
    LogUtils.d('ChatService开始初始化', tag: 'ChatService.initialize');

    // 使用Message Channel进行聊天功能
    // Message Channel 是 Agora RTM 的基础功能，无需额外配置
    LogUtils.d('使用Message Channel进行聊天功能', tag: 'ChatService.initialize');

    // 设置事件监听
    LogUtils.d('设置事件监听', tag: 'ChatService.initialize');
    _setupEventListeners();

    LogUtils.d('ChatService初始化完成（使用Message Channel）',
        tag: 'ChatService.initialize');
    return const Right(null);
  }

  /// 发送文字消息
  Future<VoidResult> sendTextMessage({
    required String content,
    required int senderId,
    required String senderName,
    String? senderAvatar,
    String? roomId,
  }) async {
    final chatMessage = ChatMessageModelExtension.createTextMessage(
      content: content,
      senderId: senderId,
      senderName: senderName,
      senderAvatar: senderAvatar,
      roomId: roomId,
    );

    // 使用Message Channel发送聊天消息
    // 将ChatMessageModel转换为RoomMessageModel来使用现有的sendChannelMessage方法
    final roomMessage = _convertChatMessageToRoomMessage(chatMessage);
    final result = await _rtmService.sendChannelMessage(roomMessage);

    // 如果发送成功，将消息添加到本地流中
    // 统一消息provider会通过ID去重，避免重复显示
    if (result.isRight()) {
      _messageStream.add(chatMessage);
    }

    return result;
  }

  /// 添加系统消息
  void addSystemMessage(String content, {String? roomId}) {
    _messageHandler.addSystemMessage(content, roomId: roomId);
  }

  /// 处理房间消息（将系统消息转换为聊天消息）
  void handleRoomMessage(RoomMessageModel roomMessage) {
    _messageHandler.handleRoomMessage(roomMessage);
  }

  /// 设置事件监听
  void _setupEventListeners() {
    // 创建并保存聊天消息监听器的引用
    _chatMessageListener = (event) {
      LogUtils.d(
          'ChatService Message事件: channelType=${event.channelType}, channelName=${event.channelName}',
          tag: 'ChatService.onMessageEvent');

      // 处理来自Message Channel的聊天消息
      _handleMessageChannelEvent(event);
    };

    // 添加聊天消息监听器（不覆盖现有的监听器）
    _rtmService.addMessageEventListener(_chatMessageListener);
  }

  /// 处理Message Channel事件
  void _handleMessageChannelEvent(MessageEvent event) {
    // 只处理来自Message Channel的消息
    if (event.channelType != RtmChannelType.message) {
      LogUtils.d('Ignoring non-message channel event', tag: 'ChatService');
      return;
    }

    // 尝试解析为聊天消息
    final chatMessage = _parseMessageChannelEvent(event);
    if (chatMessage != null) {
      LogUtils.d('Received chat message: ${chatMessage.content}',
          tag: 'ChatService');
      _messageStream.add(chatMessage);
    }
  }

  /// 离开聊天
  Future<VoidResult> leave() async {
    // 取消事件监听
    await _topicEventSubscription?.cancel();
    await _messageEventSubscription?.cancel();

    // 移除聊天消息监听器
    _rtmService.removeMessageEventListener(_chatMessageListener);

    // 使用Message Channel，不需要特殊的离开操作
    LogUtils.d('Chat service left', tag: 'ChatService.leave');
    return const Right(null);
  }

  /// 清理资源
  void dispose() {
    _topicEventSubscription?.cancel();
    _messageEventSubscription?.cancel();
    _messageStream.dispose();
  }

  /// 获取消息流状态
  bool get isStreamClosed => _messageStream.isClosed;

  /// 手动添加消息到流（用于测试或特殊情况）
  void addMessage(ChatMessageModel message) {
    _messageStream.add(message);
  }

  /// 创建并发送系统通知
  void sendSystemNotification(String content, {String? roomId}) {
    final message = ChatMessageModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      senderId: 0,
      senderName: 'System',
      timestamp: DateTime.now().millisecondsSinceEpoch,
      type: ChatMessageType.notification,
      roomId: roomId,
    );
    _messageStream.add(message);
  }

  /// 批量添加历史消息
  void addHistoryMessages(List<ChatMessageModel> messages) {
    for (final message in messages) {
      _messageStream.add(message);
    }
  }

  /// 将ChatMessageModel转换为RoomMessageModel
  RoomMessageModel _convertChatMessageToRoomMessage(
      ChatMessageModel chatMessage) {
    return RoomMessageModel(
      id: chatMessage.id,
      roomId: chatMessage.roomId,
      createAt: chatMessage.timestamp,
      senderId: chatMessage.senderId,
      event: RoomMessageEvent.textChat,
      content: chatMessage.content,
      extra: jsonEncode({
        'chatMessageType': chatMessage.type.name,
        'senderName': chatMessage.senderName,
        'senderAvatar': chatMessage.senderAvatar,
      }),
    );
  }

  /// 解析Message Channel事件为聊天消息
  ChatMessageModel? _parseMessageChannelEvent(MessageEvent event) {
    try {
      // 检查消息是否为空
      if (event.message == null) {
        LogUtils.d('Message is null',
            tag: 'ChatService._parseMessageChannelEvent');
        return null;
      }

      // 解析消息内容
      String messageContent;
      if (event.message is String) {
        messageContent = event.message as String;
      } else if (event.message is Uint8List) {
        // Message Channel 发送的消息是 Uint8List 格式
        try {
          messageContent = utf8.decode(event.message as Uint8List);
          LogUtils.d('Decoded Uint8List message: $messageContent',
              tag: 'ChatService._parseMessageChannelEvent');
        } catch (e) {
          LogUtils.e('Failed to decode Uint8List message: $e',
              tag: 'ChatService._parseMessageChannelEvent');
          return null;
        }
      } else {
        LogUtils.d(
            'Message is not a string or Uint8List: ${event.message.runtimeType}',
            tag: 'ChatService._parseMessageChannelEvent');
        return null;
      }

      // 尝试解析为RoomMessageModel
      final messageJson = jsonDecode(messageContent) as Map<String, dynamic>;
      final roomMessage = RoomMessageModel.fromJson(messageJson);

      // 检查是否为文字聊天消息
      if (roomMessage.event != RoomMessageEvent.textChat) {
        LogUtils.d('Not a text chat message: ${roomMessage.event}',
            tag: 'ChatService._parseMessageChannelEvent');
        return null;
      }

      // 解析额外信息
      Map<String, dynamic>? extraData;
      if (roomMessage.extra != null) {
        try {
          extraData = jsonDecode(roomMessage.extra!) as Map<String, dynamic>;
        } catch (e) {
          LogUtils.d('Failed to parse extra data: $e',
              tag: 'ChatService._parseMessageChannelEvent');
        }
      }

      // 转换为ChatMessageModel
      return ChatMessageModel(
        id: roomMessage.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
        content: roomMessage.content ?? '',
        senderId: roomMessage.senderId ?? 0,
        senderName: extraData?['senderName'] ?? 'Unknown',
        senderAvatar: extraData?['senderAvatar'],
        timestamp:
            roomMessage.createAt ?? DateTime.now().millisecondsSinceEpoch,
        type: _parseMessageType(extraData?['chatMessageType']),
        roomId: roomMessage.roomId,
      );
    } catch (e) {
      LogUtils.e('Failed to parse message channel event: $e',
          tag: 'ChatService._parseMessageChannelEvent');
      return null;
    }
  }

  /// 解析消息类型
  ChatMessageType _parseMessageType(String? typeString) {
    if (typeString == null) return ChatMessageType.text;

    switch (typeString) {
      case 'text':
        return ChatMessageType.text;
      case 'system':
        return ChatMessageType.system;
      case 'notification':
        return ChatMessageType.notification;
      default:
        return ChatMessageType.text;
    }
  }
}

/// 聊天服务工厂
class ChatServiceFactory {
  /// 创建聊天服务实例
  static ChatService create(IRtmService rtmService) {
    return ChatService(rtmService);
  }

  /// 创建并初始化聊天服务
  static Future<Either<Exception, ChatService>> createAndInitialize(
    IRtmService rtmService,
  ) async {
    final chatService = ChatService(rtmService);
    final result = await chatService.initialize();

    return result.fold(
      (error) => Left(error),
      (_) => Right(chatService),
    );
  }
}
