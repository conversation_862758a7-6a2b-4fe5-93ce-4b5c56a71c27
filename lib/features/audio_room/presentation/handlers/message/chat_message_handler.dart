import 'dart:convert';
import 'dart:typed_data';

import 'package:agora_rtm/agora_rtm.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_enums.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/domain/services/chat_message_stream.dart';

/// 聊天消息处理器
/// 处理来自Stream Channel的文字聊天消息
class ChatMessageHandler {
  final ChatMessageStream _chatMessageStream;

  ChatMessageHandler(this._chatMessageStream);

  /// 处理Topic消息事件
  void handleTopicMessage(TopicEvent event) {
    LogUtils.d('TopicEvent received: ${event.toJson()}',
        tag: 'ChatMessageHandler');

    // TopicEvent 主要用于处理 topic 状态变化，实际的消息通过 MessageEvent 处理
    // 这里我们记录事件但不处理消息内容
    LogUtils.d(
        'Topic event type: ${event.type}, topicInfos: ${event.topicInfos?.length ?? 0}',
        tag: 'ChatMessageHandler');
  }

  /// 处理消息事件（从 MessageEvent 中提取聊天消息）
  void handleMessageEvent(MessageEvent event) {
    LogUtils.d(
        'MessageEvent received: channelType=${event.channelType}, channelName=${event.channelName}, topicName=${event.channelTopic}',
        tag: 'ChatMessageHandler');

    // 只处理来自 Stream Channel 的消息
    if (event.channelType != RtmChannelType.stream) {
      LogUtils.d('Ignoring non-stream message', tag: 'ChatMessageHandler');
      return;
    }

    // 只处理 text_chat topic 的消息
    if (event.channelTopic != 'text_chat') {
      LogUtils.d('Ignoring non-text-chat topic: ${event.channelTopic}',
          tag: 'ChatMessageHandler');
      return;
    }

    // 解析消息
    final message = _parseMessageEvent(event);
    if (message != null) {
      LogUtils.d(
          'Adding chat message from ${message.senderName}: ${message.content}',
          tag: 'ChatMessageHandler');
      _chatMessageStream.add(message);
    }
  }

  /// 解析消息事件
  ChatMessageModel? _parseMessageEvent(MessageEvent event) {
    try {
      // 检查消息类型
      if (event.messageType != RtmMessageType.binary) {
        LogUtils.d('Ignoring non-binary message type: ${event.messageType}',
            tag: 'ChatMessageHandler');
        return null;
      }

      // 获取消息数据
      final messageData = event.message;
      if (messageData == null) {
        LogUtils.d('Message data is null', tag: 'ChatMessageHandler');
        return null;
      }

      // 解析二进制消息
      Uint8List? binaryData;
      binaryData = messageData;

      // 解码消息
      final messageString = utf8.decode(binaryData);
      final messageJson = json.decode(messageString) as Map<String, dynamic>;

      LogUtils.d('Parsed message JSON: $messageJson',
          tag: 'ChatMessageHandler');

      return ChatMessageModel.fromJson(messageJson);
    } catch (e) {
      LogUtils.e('Failed to parse chat message: $e', tag: 'ChatMessageHandler');
      return null;
    }
  }

  /// 将房间消息转换为聊天消息（用于系统消息整合）
  ChatMessageModel? convertRoomMessageToChatMessage(
      RoomMessageModel roomMessage) {
    // 只处理系统相关的消息
    if (roomMessage.event == null) return null;

    String? content;
    ChatMessageType type = ChatMessageType.system;

    switch (roomMessage.event!) {
      case RoomMessageEvent.userStatus:
        content = _buildUserStatusMessage(roomMessage);
        break;
      case RoomMessageEvent.systemMessage:
        content = roomMessage.content;
        break;
      case RoomMessageEvent.actionMessage:
        content = _buildActionMessage(roomMessage);
        type = ChatMessageType.notification;
        break;
      case RoomMessageEvent.textChat:
        // 处理文字聊天消息
        content = roomMessage.content;
        type = ChatMessageType.text;
        break;
      default:
        return null; // 其他类型的消息不转换
    }

    if (content == null || content.isEmpty) return null;

    return ChatMessageModel(
      id: roomMessage.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      senderId: roomMessage.senderId ?? 0,
      senderName: roomMessage.sender?.firstName ??
          (type == ChatMessageType.text ? 'Unknown' : 'System'),
      senderAvatar: roomMessage.sender?.avatarUrl,
      timestamp: roomMessage.createAt ?? DateTime.now().millisecondsSinceEpoch,
      type: type,
      roomId: roomMessage.roomId,
      extra: {
        'originalEvent': roomMessage.event!.name,
        'originalSubtype': roomMessage.eventSubtype?.name,
        'targetUserAvatar': roomMessage.targetUser?.avatarUrl,
      },
    );
  }

  /// 构建用户状态消息
  String? _buildUserStatusMessage(RoomMessageModel message) {
    final userName = message.sender?.firstName ?? 'Unknown User';

    switch (message.eventSubtype) {
      case RoomMessageEventSubtype.joinRoom:
        return '$userName join the room';
      case RoomMessageEventSubtype.dropOnMic:
        return '$userName drop the mic';
      case RoomMessageEventSubtype.quitManager:
        return '$userName quit the manager';
      default:
        return null;
    }
  }

  /// 构建操作消息
  String? _buildActionMessage(RoomMessageModel message) {
    final userName = message.sender?.firstName ?? 'Unknown User';
    final targetName = message.targetUser?.firstName ?? 'Unknown User';

    switch (message.eventSubtype) {
      case RoomMessageEventSubtype.micRequest:
        return '$userName request to join the mic';
      case RoomMessageEventSubtype.agreeMicRequest:
        return '$userName agree $targetName to join the mic';
      case RoomMessageEventSubtype.rejectMicRequest:
        return '$userName reject $targetName to join the mic';
      case RoomMessageEventSubtype.inviteOnMic:
        return '$userName invite $targetName to join the mic';
      case RoomMessageEventSubtype.agreeInviteOnMic:
        return '$targetName agree mic invite';
      case RoomMessageEventSubtype.rejectInviteOnMic:
        return '$targetName reject mic invite';
      case RoomMessageEventSubtype.inviteManager:
        return '$userName invite $targetName to be manager';
      case RoomMessageEventSubtype.agreeInviteManager:
        return '$targetName agree manager invite';
      case RoomMessageEventSubtype.rejectInviteManager:
        return '$targetName reject manager invite';
      case RoomMessageEventSubtype.removeManager:
        return '$userName remove $targetName from manager';
      case RoomMessageEventSubtype.muteMic:
        return '$userName mute mic';
      case RoomMessageEventSubtype.unmuteMic:
        return '$userName unmute mic';
      case RoomMessageEventSubtype.kickOut:
        return '$targetName has been kicked out';
      case RoomMessageEventSubtype.levelup:
        return '$userName level up';
      default:
        return null;
    }
  }

  /// 添加系统消息到聊天流
  void addSystemMessage(String content, {String? roomId}) {
    final message = ChatMessageModelExtension.createSystemMessage(
      content: content,
      roomId: roomId,
    );
    _chatMessageStream.add(message);
  }

  /// 处理房间消息并转换为聊天消息
  void handleRoomMessage(RoomMessageModel roomMessage) {
    final chatMessage = convertRoomMessageToChatMessage(roomMessage);
    if (chatMessage != null) {
      _chatMessageStream.add(chatMessage);
    }
  }
}
