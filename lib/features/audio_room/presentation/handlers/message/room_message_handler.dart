import 'dart:async';
import 'dart:convert';

import 'package:agora_rtm/agora_rtm.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/audio_room/constants/room_constants.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_enums.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_user.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_audio_room_service.dart';
import 'package:flutter_audio_room/features/audio_room/domain/repositories/audio_room_repository.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_state.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/error_handler.dart';

class RoomMessageHandler {
  final IAudioRoomService _audioRoomService;
  final AudioRoomRepository _audioRoomRepository;
  final void Function(RoomMessageModel) _addMessage;
  final StreamController<RoomMessageModel> _messageStream;

  RoomMessageHandler(
    this._audioRoomService,
    this._audioRoomRepository,
    this._addMessage,
    this._messageStream,
  );

  Future<ResultWithData<AudioRoomState>> handleMessage(
      MessageEvent event, AudioRoomState currentState) async {
    if (event.message == null) return Right(currentState);

    final message = _parseMessage(event.message!);
    if (message == null) return Right(currentState);

    _messageStream.add(message);

    if (message.event == RoomMessageEvent.actionMessage) {
      _addMessage(message);
      switch (message.eventSubtype) {
        case RoomMessageEventSubtype.micRequest:
        case RoomMessageEventSubtype.inviteOnMic:
        case RoomMessageEventSubtype.inviteManager:
        case RoomMessageEventSubtype.kickOut:
          return _handleMessages(message, event.publisher ?? '', currentState);
        case RoomMessageEventSubtype.agreeInviteOnMic:
          return _useAgreeInviteOnMic(message, currentState);
        case RoomMessageEventSubtype.changePositionRequest:
          return _handlePositionChange(message, currentState);
        case RoomMessageEventSubtype.userMuteMic:
          return _handleMuteState(message, currentState, true, false);
        case RoomMessageEventSubtype.userUnmuteMic:
          return _handleMuteState(message, currentState, false, false);
        case RoomMessageEventSubtype.muteMic:
          return _handleMuteState(message, currentState, false, true);
        case RoomMessageEventSubtype.unmuteMic:
          return _handleMuteState(message, currentState, false, false);
        case RoomMessageEventSubtype.agreeInviteManager:
          return _handleManagerInvite(message, currentState, true);
        case RoomMessageEventSubtype.rejectInviteManager:
          return _handleManagerInvite(message, currentState, false);
        default:
          return Right(currentState);
      }
    } else if (message.event == RoomMessageEvent.giftMessage) {
      _addMessage(message);
    } else if (message.event == RoomMessageEvent.followBack) {
      _addMessage(message);
    }
    return Right(currentState);
  }

  RoomMessageModel? _parseMessage(List<int> messageData) {
    try {
      return RoomMessageModel.fromJson(json.decode(utf8.decode(messageData)));
    } catch (e) {
      LogUtils.e('Failed to parse message',
          error: e, tag: 'room_message_handler._parseMessage');
      return null;
    }
  }

  ResultWithData<AudioRoomState> _handleMessages(
    RoomMessageModel message,
    String publisher,
    AudioRoomState currentState,
  ) {
    final publisherId = int.tryParse(publisher);
    if (publisherId == null) {
      return Left(ErrorHandler.createValidationError(message.toString()));
    }

    return Right(currentState);
  }

  Future<ResultWithData<AudioRoomState>> _handlePositionChange(
    RoomMessageModel message,
    AudioRoomState currentState,
  ) async {
    final oldPosition = message.position?.oldPosition ?? -1;
    final newPosition = message.position?.targetPosition ?? -1;

    if (oldPosition <= 0 ||
        newPosition <= 0 ||
        oldPosition == newPosition ||
        newPosition > RoomConstants.maxSeatPosition) {
      return Left(ErrorHandler.createValidationError('Invalid position'));
    }

    final newUsers = Map<int, int>.from(currentState.users);
    final newMembers = Map<int, RoomUser>.from(currentState.members);

    final result = await _audioRoomService.setChannelMultiMetadata(
      [
        MetadataItem(
          key: RtmMetadataKey.seats()[oldPosition],
          value: 'null',
        ),
        MetadataItem(
          key: RtmMetadataKey.seats()[newPosition],
          value: message.senderId.toString(),
        ),
      ],
    );
    if (result.isLeft()) {
      return Left(ErrorHandler.createValidationError('Failed to set position'));
    }

    final uid = int.tryParse(message.senderId.toString());
    if (uid != null) {
      final user = newMembers[uid];
      if (user != null) {
        newMembers[uid] = user.copyWith(position: newPosition);
      }
    }

    return Right(currentState.copyWith(
      members: newMembers,
      users: newUsers,
    ));
  }

  Future<ResultWithData<AudioRoomState>> _handleMuteState(
    RoomMessageModel message,
    AudioRoomState state,
    bool isMutedBySelf,
    bool isMutedByManager,
  ) async {
    final uid = message.targetId;
    if (uid == null) {
      return Left(ErrorHandler.createValidationError('Invalid target id'));
    }

    var newState = state.copyWith(
      members: Map<int, RoomUser>.from(state.members),
    );

    var user = newState.members[uid];

    if (user != null) {
      user = user.copyWith(
        isMutedBySelf: isMutedBySelf,
        isMutedByManager: isMutedByManager,
      );
      newState = newState.copyWith(
        members: {
          ...newState.members,
          uid: user,
        },
      );

      // if the user is the current user, update the metadata
      if (uid == state.currentUid) {
        newState = newState.copyWith(
          currentUser: user,
        );
        final result = await _audioRoomService.setUserMetadata(user);
        if (result.isLeft()) {
          return Left(
              ErrorHandler.createValidationError('Failed to set metadata'));
        }
        final muteResult = await _audioRoomService.muteLocalAudio(
          isMutedBySelf || isMutedByManager,
        );
        if (muteResult.isLeft()) {
          return Left(
            ErrorHandler.createValidationError('Failed to mute audio'),
          );
        }
      }
    }

    return Right(newState);
  }

  Future<ResultWithData<AudioRoomState>> _useAgreeInviteOnMic(
      RoomMessageModel message, AudioRoomState currentState) async {
    if (!currentState.isCreator && !currentState.isManager) {
      return Left(ErrorHandler.createValidationError('Invalid user role'));
    }

    final senderUid = message.senderId;
    if (senderUid == null) {
      return Left(ErrorHandler.createValidationError('Invalid sender id'));
    }

    final targetSeat = currentState.firstEmptySeat();
    if (targetSeat == -1) {
      return Left(ErrorHandler.createValidationError('No empty seat found'));
    }

    final result = await _audioRoomService.setChannelMultiMetadata(
      [
        MetadataItem(
          key: RtmMetadataKey.seats()[targetSeat],
          value: senderUid.toString(),
        ),
      ],
    );
    if (result.isLeft()) {
      return Left(ErrorHandler.createValidationError('Failed to set metadata'));
    }

    final newMembers = Map<int, RoomUser>.from(currentState.members);
    final user = newMembers[senderUid];
    if (user != null) {
      newMembers[senderUid] = user.copyWith(position: targetSeat);
    }

    final newUsers = Map<int, int>.from(currentState.users);
    newUsers[targetSeat] = senderUid;

    return Right(currentState.copyWith(
      members: newMembers,
      users: newUsers,
    ));
  }

  Future<ResultWithData<AudioRoomState>> _handleManagerInvite(
    RoomMessageModel message,
    AudioRoomState currentState,
    bool accept,
  ) async {
    if (!currentState.isCreator) {
      return Left(ErrorHandler.createValidationError('Invalid user role'));
    }

    if (currentState.manager != null) {
      return Left(ErrorHandler.createValidationError('Already has manager'));
    }

    if (!accept) {
      return Left(ErrorHandler.createValidationError('Not accept'));
    }

    final uid = message.senderId;

    final setManagerResult = await _audioRoomRepository.setManager(
      roomId: currentState.currentRoom?.id ?? '',
      targetUserId: currentState.members[uid]?.userId ?? '',
    );
    if (setManagerResult.isLeft()) {
      return Left(ErrorHandler.createValidationError('Failed to set manager'));
    }

    final result = await _audioRoomService.setChannelMultiMetadata(
      [
        MetadataItem(
          key: RtmMetadataKey.managerId.name,
          value: uid.toString(),
        ),
      ],
    );
    if (result.isLeft()) {
      return Left(ErrorHandler.createValidationError('Failed to set metadata'));
    }

    return Right(currentState);
  }
}

