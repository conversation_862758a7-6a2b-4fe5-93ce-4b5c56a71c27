import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_audio_room_service.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_rtc_service.dart';
import 'package:flutter_audio_room/features/audio_room/domain/repositories/audio_room_repository.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/handlers/base/room_event_handler_interface.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_state.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/utils/callback_handlers.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/utils/default_room_message.dart';
import 'package:flutter_audio_room/services/crypto_service/i_crypto_service.dart';
import 'package:flutter_audio_room/services/gift_service/domain/repositories/i_gift_repository.dart';
import 'package:flutter_audio_room/services/permission_service/permission_service.dart';
import 'package:flutter_audio_room/services/screen_protector_service/i_screen_protector_service.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

mixin AudioRoomProviderBaseMixin on Notifier<AudioRoomState> {
  StreamController<RoomMessageModel> get messageStream;

  @protected
  PermissionService get permissionService;
  @protected
  IAudioRoomService get audioService;
  @protected
  DefaultRoomMessage get defaultRoomMessage;
  @protected
  StorageService get storageService;
  @protected
  ICryptoService get cryptoService;
  @protected
  IScreenProtectorService get screenProtectorService;
  @protected
  CallbackQueue get callbackQueue;
  @protected
  RoomEventHandlerInterface get eventHandler;
  @protected
  AudioRoomRepository get repository;
  @protected
  Map<String, ConnectionStateChangedCallback>
      get connectionStateChangedCallbacks;
  @protected
  AudioVolumeCallback? get audioVolumeCallback;
  @protected
  IGiftRepository get giftRepository;

  /// 添加消息到统一消息provider
  @protected
  void addMessage(RoomMessageModel message);
}
